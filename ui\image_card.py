from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QSizePolicy
from PySide6.QtCore import Qt, QSize, QThread, Signal
from PySide6.QtGui import QPixmap, QImage
from qfluentwidgets import CardWidget, BodyLabel, CaptionLabel
import os
import imghdr
import logging
from .theme import isDarkTheme

logger = logging.getLogger(__name__)

class ImageLoader(QThread):
    """图片加载线程"""
    imageLoaded = Signal(QImage)
    loadFailed = Signal(str)

    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path

    def run(self):
        try:
            image = QImage(self.file_path)
            if not image.isNull():
                self.imageLoaded.emit(image)
            else:
                self.loadFailed.emit("图片加载失败")
        except Exception as e:
            self.loadFailed.emit(str(e))

class ImageCard(CardWidget):
    """图片卡片组件"""
    def __init__(self, file_path: str, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.init_ui()
        self.load_image()

    def init_ui(self):
        # 设置卡片大小策略 - 瀑布流需要动态高度
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)

        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)  # 添加一些内边距
        layout.setSpacing(0)  # 去除间距，让图片占满卡片

        # 创建图片标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setScaledContents(True)  # 允许图片缩放

        # 设置磨砂玻璃半透明风格
        if isDarkTheme():
            # 深色主题：深色磨砂玻璃效果
            bg_color = "rgba(44, 44, 44, 0.7)"  # 半透明深灰色
            border_color = "rgba(60, 60, 60, 0.8)"  # 稍亮的边框
        else:
            # 浅色主题：浅色磨砂玻璃效果
            bg_color = "rgba(245, 245, 245, 0.7)"  # 半透明浅灰色
            border_color = "rgba(220, 220, 220, 0.8)"  # 稍暗的边框

        self.image_label.setStyleSheet(f"""
            QLabel {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 8px;
                padding: 2px;
            }}
        """)
        layout.addWidget(self.image_label)

        # 设置整个卡片的磨砂玻璃半透明风格
        if isDarkTheme():
            # 深色主题
            card_bg = "rgba(30, 30, 30, 0.6)"  # 更透明的深色背景
            shadow = "rgba(0, 0, 0, 0.3)"  # 暗色阴影
        else:
            # 浅色主题
            card_bg = "rgba(255, 255, 255, 0.6)"  # 更透明的浅色背景
            shadow = "rgba(0, 0, 0, 0.15)"  # 浅色阴影

        self.setStyleSheet(f"""
            QWidget {{
                background-color: transparent;
            }}

            CardWidget {{
                background-color: {card_bg};
                border-radius: 10px;
                margin: 2px;
                padding: 4px;
                border: 1px solid {shadow};
            }}
        """)

    def is_valid_image(self, file_path: str) -> bool:
        """检查文件是否为有效的图片文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                return False

            # 检查文件大小
            if os.path.getsize(file_path) == 0:
                logger.warning(f"文件大小为0: {file_path}")
                return False

            # 检查文件格式
            image_type = imghdr.what(file_path)
            is_valid = image_type in ['jpeg', 'png', 'gif', 'bmp']
            logger.info(f"文件 {file_path} 的格式为 {image_type}, 是否有效: {is_valid}")
            return is_valid
        except Exception as e:
            logger.error(f"检查图片有效性时出错: {str(e)}")
            return False

    def load_image(self):
        """异步加载并显示图片"""
        if not self.is_valid_image(self.file_path):
            self.image_label.setText("无效的图片文件")
            return

        # 显示加载中提示
        self.image_label.setText("加载中...")

        # 创建加载线程
        self.loader = ImageLoader(self.file_path)
        self.loader.imageLoaded.connect(self.on_image_loaded)
        self.loader.loadFailed.connect(self.on_load_failed)
        self.loader.start()

    def on_image_loaded(self, image):
        """图片加载完成的回调"""
        try:
            # 获取当前卡片的宽度
            card_width = self.width() if self.width() > 0 else 250

            # 获取图片的原始尺寸
            original_width = image.width()
            original_height = image.height()

            # 计算图片的宽高比
            aspect_ratio = original_height / original_width

            # 根据卡片宽度和宽高比计算图片高度
            image_height = int(card_width * aspect_ratio)

            # 设置图片标签的高度
            self.image_label.setFixedHeight(image_height)

            # 缩放图片以适应标签大小
            scaled_image = image.scaled(
                card_width, image_height,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )

            # 显示图片
            self.image_label.setPixmap(QPixmap.fromImage(scaled_image))

            # 调整卡片的总高度
            total_height = image_height + 16  # 加上边距
            self.setFixedHeight(total_height)

            # 通知父组件更新布局
            if self.parent() and hasattr(self.parent(), 'update_layout'):
                self.parent().update_layout()

            logger.info(f"图片加载成功: {self.file_path}")

        except Exception as e:
            logger.error(f"显示图片失败: {str(e)}")
            self.image_label.setText("显示失败")

    def on_load_failed(self, error_msg):
        """图片加载失败的回调"""
        logger.error(f"加载图片失败: {error_msg}")
        self.image_label.setText("加载失败")

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'loader'):
            self.loader.quit()
            self.loader.wait()