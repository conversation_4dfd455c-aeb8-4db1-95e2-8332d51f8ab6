from PySide6.QtWidgets import QWidget, QVBoxLayout, QScrollArea, QSizePolicy, QHBoxLayout
from PySide6.QtCore import Qt, Signal, QRect
from PySide6.QtGui import QImageReader
from qfluentwidgets import ScrollArea
from .theme import isDarkTheme
import os
import logging
from .image_card import ImageCard
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WaterfallLayout(QWidget):
    """瀑布流布局组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.columns = 3  # 默认3列
        self.spacing = 10  # 间距
        self.items = []  # 存储所有项目
        self.column_heights = []  # 每列的高度

    def set_columns(self, columns):
        """设置列数"""
        self.columns = columns
        self.column_heights = [0] * columns
        self.update_layout()

    def add_item(self, widget):
        """添加项目到瀑布流"""
        self.items.append(widget)
        widget.setParent(self)
        self.update_layout()

    def clear_items(self):
        """清空所有项目"""
        for item in self.items:
            item.deleteLater()
        self.items.clear()
        self.column_heights = [0] * self.columns

    def update_layout(self):
        """更新布局"""
        if not self.items:
            return

        # 重置列高度
        self.column_heights = [0] * self.columns

        # 计算每列的宽度
        total_width = self.width()
        if total_width <= 0:
            return

        column_width = max(200, (total_width - (self.columns + 1) * self.spacing) // self.columns)

        for i, item in enumerate(self.items):
            # 找到最短的列
            min_height_col = self.column_heights.index(min(self.column_heights))

            # 计算位置
            x = self.spacing + min_height_col * (column_width + self.spacing)
            y = self.column_heights[min_height_col] + self.spacing

            # 设置项目宽度
            item.setFixedWidth(column_width)

            # 如果是图片卡片，需要重新计算高度
            if hasattr(item, 'image_label') and hasattr(item, 'loader'):
                # 等待图片加载完成后再设置位置
                item.move(x, y)
                item.show()
                # 使用当前高度，如果图片还没加载完成，会在加载完成后更新
                item_height = item.height() if item.height() > 0 else 200
            else:
                item.move(x, y)
                item.show()
                item_height = item.height()

            # 更新列高度
            self.column_heights[min_height_col] += item_height + self.spacing

        # 设置容器的总高度
        max_height = max(self.column_heights) if self.column_heights else 0
        self.setMinimumHeight(max_height + self.spacing)

    def resizeEvent(self, event):
        """窗口大小改变时重新布局"""
        super().resizeEvent(event)
        self.update_layout()

class ImageGallery(ScrollArea):
    """图片展示区组件 - 瀑布流显示"""
    # 定义信号
    image_loaded = Signal(int)  # 图片加载完成信号
    images_loaded_signal = Signal()  # 所有图片加载完成信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.images = []
        self.max_images = 50  # 增加最大图片数量限制

    def init_ui(self):
        # 设置滚动区域属性
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 设置背景透明
        self.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QWidget#waterfall_widget {
                background-color: transparent;
            }
        """)

        # 创建瀑布流容器
        self.waterfall_widget = WaterfallLayout()
        self.waterfall_widget.setObjectName("waterfall_widget")

        # 设置瀑布流的列数（可以根据窗口大小动态调整）
        self.waterfall_widget.set_columns(4)  # 默认4列

        # 设置内容区域的样式，与磨砂玻璃风格协调
        if isDarkTheme():
            # 深色主题：深色渐变背景
            bg_gradient = "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(30, 30, 40, 0.7), stop:0.5 rgba(40, 40, 50, 0.5), stop:1 rgba(30, 30, 40, 0.7))"
        else:
            # 浅色主题：浅色渐变背景
            bg_gradient = "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(240, 240, 245, 0.7), stop:0.5 rgba(250, 250, 255, 0.5), stop:1 rgba(240, 240, 245, 0.7))"

        self.waterfall_widget.setStyleSheet(f"""
            QWidget#waterfall_widget {{
                background: {bg_gradient};
                border-radius: 15px;
                padding: 10px;
            }}
        """)

        self.setWidget(self.waterfall_widget)

    def resizeEvent(self, event):
        """窗口大小改变时调整列数"""
        super().resizeEvent(event)
        if hasattr(self, 'waterfall_widget'):
            # 根据窗口宽度动态调整列数
            width = self.width()
            if width < 600:
                columns = 2
            elif width < 900:
                columns = 3
            elif width < 1200:
                columns = 4
            else:
                columns = 5
            self.waterfall_widget.set_columns(columns)

    def is_valid_aspect_ratio(self, image_path: str) -> bool:
        """检查图片是否有效，不再严格限制比例"""
        try:
            reader = QImageReader(image_path)
            size = reader.size()
            if size.isValid():
                # 只要图片尺寸有效，就认为是有效的图片
                # 不再严格限制比例，以便加载更多图片
                width = size.width()
                height = size.height()

                # 只排除极端比例的图片
                ratio = width / height
                is_valid = 0.1 <= ratio <= 10  # 允许更宽泛的比例范围

                logger.info(f"图片 {os.path.basename(image_path)} 的尺寸为 {width}x{height}, 比例: {ratio:.3f}, 是否有效: {is_valid}")
                return is_valid
            else:
                logger.warning(f"无法获取图片 {os.path.basename(image_path)} 的尺寸")
        except Exception as e:
            logger.error(f"检查图片有效性时出错: {str(e)}")
        return False

    def load_images(self, directory: str):
        """加载指定目录下的所有图片"""
        # 清空现有图片
        self.clear_images()

        # 获取所有图片文件
        image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp')
        valid_images = []

        logger.info(f"开始扫描目录: {directory}")
        for root, _, files in os.walk(directory):
            for file in files:
                if file.lower().endswith(image_extensions):
                    image_path = os.path.join(root, file)
                    if os.path.exists(image_path) and os.path.getsize(image_path) > 0:
                        # 检查图片比例
                        if self.is_valid_aspect_ratio(image_path):
                            valid_images.append(image_path)
                            # 达到最大数量限制时停止
                            if len(valid_images) >= self.max_images:
                                logger.info("已达到最大图片数量限制")
                                break
            # 如果已经达到最大数量限制，停止遍历
            if len(valid_images) >= self.max_images:
                break

        logger.info(f"找到 {len(valid_images)} 张有效图片")
        # 添加有效的图片
        for image_path in valid_images:
            self.add_image(image_path)

        # 图片加载完成后发出信号
        if self.images:
            self.images_loaded_signal.emit()

    def add_image(self, image_path: str):
        """添加单个图片到瀑布流"""
        card = ImageCard(image_path)
        self.waterfall_widget.add_item(card)
        self.images.append(card)
        self.image_loaded.emit(len(self.images))

    def clear_images(self):
        """清空所有图片"""
        # 清空瀑布流中的图片
        self.waterfall_widget.clear_items()
        self.images.clear()

    # 保留兼容性方法，但不执行任何操作
    def set_motion_mode(self, mode: str, speed: int = 50):
        """设置运动模式 - 已弃用"""
        pass

    def start_auto_scroll(self, interval: int = 5000):
        """开始自动滚动 - 已弃用"""
        pass