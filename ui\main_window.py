from PySide6.QtWidgets import Q<PERSON>ain<PERSON><PERSON>ow, <PERSON>Widget, QVBoxLayout, QHBoxLayout, QFormLayout
from PySide6.QtCore import Qt, QThread
from qfluentwidgets import (FluentWindow, NavigationInterface, NavigationItemPosition,
                          SubtitleLabel, setTheme, Theme, FluentIcon,
                          PrimaryPushButton, InfoBar, InfoBarPosition,
                          ScrollArea, PushButton, ComboBox, LineEdit, CheckBox)
from qfluentwidgets.common.config import qconfig
from .download_manager import DownloadManager
from .image_gallery import ImageGallery
from .settings_page import SettingsPage
import os
import json

class MainWindow(FluentWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CivitAI Image Grabber")
        self.resize(1000, 700)

        # 初始化设置
        self.settings = self.load_settings()

        # 初始化下载管理器
        self.download_manager = DownloadManager()
        self.download_manager.log_signal.connect(self.on_log_message)
        self.download_manager.download_complete.connect(self.on_download_complete)
        self.download_manager.download_error.connect(self.on_download_error)

        # 初始化界面
        self.init_interface()

        # 设置主题
        self.theme = qconfig.theme
        setTheme(self.theme)

    def init_interface(self):
        # 创建导航栏
        self.navigation_interface = NavigationInterface(self, showMenuButton=True)
        self.navigation_interface.setFixedWidth(200)

        # 添加导航项
        self.navigation_interface.addItem(
            routeKey='home',
            icon=FluentIcon.HOME,
            text='主页',
            position=NavigationItemPosition.TOP,
            onClick=lambda: self.on_navigation_changed('home')
        )

        self.navigation_interface.addItem(
            routeKey='settings',
            icon=FluentIcon.SETTING,
            text='设置',
            position=NavigationItemPosition.BOTTOM,
            onClick=lambda: self.on_navigation_changed('settings')
        )

        # 创建主内容区
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(10)

        # 下载参数区
        self.param_form = QFormLayout()
        self.param_form.setLabelAlignment(Qt.AlignRight)
        self.param_form.setFormAlignment(Qt.AlignLeft)
        self.param_form.setContentsMargins(0, 0, 0, 0)
        self.param_form.setSpacing(8)

        # 下载模式选择
        self.mode_combo = ComboBox()
        self.mode_combo.addItems(["用户名", "模型ID", "标签", "模型版本ID"])
        self.mode_combo.currentIndexChanged.connect(self.on_mode_changed)
        self.param_form.addRow("下载模式：", self.mode_combo)

        # 参数输入框（动态显示）
        self.input_line = LineEdit()
        self.param_form.addRow("用户名：", self.input_line)
        self.input_line.setPlaceholderText("多个用英文逗号分隔")

        # 画质选择
        self.quality_combo = ComboBox()
        self.quality_combo.addItems(["SD(低画质)", "HD(高画质)"])
        self.param_form.addRow("图片画质：", self.quality_combo)

        # 允许重复下载
        self.redownload_checkbox = CheckBox("允许重复下载")
        self.param_form.addRow("", self.redownload_checkbox)

        self.content_layout.addLayout(self.param_form)

        # 创建图片展示区
        self.image_gallery = ImageGallery()
        self.image_gallery.setMinimumHeight(400)
        self.content_layout.addWidget(self.image_gallery)

        # 创建下载控制区
        self.control_widget = QWidget()
        self.control_layout = QHBoxLayout(self.control_widget)
        self.control_layout.setContentsMargins(0, 0, 0, 0)

        self.download_button = PrimaryPushButton("开始下载")
        self.cancel_button = PushButton("取消下载")
        self.theme_button = PushButton("切换主题")
        self.control_layout.addWidget(self.download_button)
        self.control_layout.addWidget(self.cancel_button)
        self.control_layout.addWidget(self.theme_button)
        self.control_layout.addStretch()
        self.content_layout.addWidget(self.control_widget)

        # 日志区已移除，改为瀑布流显示

        # 创建设置页面
        self.settings_page = SettingsPage()
        self.settings_page.settings_changed.connect(self.on_settings_changed)

        # 设置主窗口布局
        self.stackedWidget.addWidget(self.content_widget)
        self.stackedWidget.addWidget(self.settings_page)
        self.navigationInterface = self.navigation_interface

        # 连接信号
        self.download_button.clicked.connect(self.on_download_clicked)
        self.cancel_button.clicked.connect(self.on_cancel_clicked)
        self.theme_button.clicked.connect(self.toggle_theme)

        # 加载默认图片目录
        if self.settings.get('image_dir'):
            self.image_gallery.load_images(self.settings['image_dir'])

    def load_settings(self) -> dict:
        """加载设置"""
        default_settings = {
            'timeout': 60,
            'semaphore_limit': 5,
            'retries': 2,
            'image_dir': '',
            'scroll_interval': 3
        }

        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    # 合并默认设置和保存的设置
                    default_settings.update(settings)
        except Exception as e:
            print(f"加载设置失败: {e}")

        return default_settings

    def save_settings(self, settings: dict):
        """保存设置"""
        try:
            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存设置失败: {e}")

    def on_settings_changed(self, settings: dict):
        """处理设置改变事件"""
        # 更新设置
        self.settings.update(settings)

        # 保存设置
        self.save_settings(self.settings)

        # 更新图片展示
        if settings.get('image_dir'):
            self.image_gallery.load_images(settings['image_dir'])
            # 不再调用自动滚动方法，改为鼠标触发滚动
            # 更新连续滚动速度
            if 'continuous_speed' in settings:
                self.image_gallery.continuous_speed = settings['continuous_speed']

    def on_mode_changed(self, idx):
        # 动态切换参数输入提示
        label_map = ["用户名：", "模型ID：", "标签：", "模型版本ID："]
        placeholder_map = ["多个用英文逗号分隔", "多个用英文逗号分隔", "多个用英文逗号分隔", "多个用英文逗号分隔"]
        self.param_form.labelForField(self.input_line).setText(label_map[idx])
        self.input_line.setPlaceholderText(placeholder_map[idx])

    def on_download_clicked(self):
        """处理下载按钮点击事件"""
        # 获取输入参数
        mode = self.mode_combo.currentIndex() + 1  # 转换为1-4的模式值
        input_text = self.input_line.text().strip()
        quality = 2 if self.quality_combo.currentIndex() == 1 else 1  # 1=SD, 2=HD
        allow_redownload = self.redownload_checkbox.isChecked()

        # 验证输入
        if not input_text:
            InfoBar.error(
                title='错误',
                content="请输入下载参数",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
            return

        # 更新UI状态
        self.download_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # 启动下载任务
        self.download_manager.start_download(
            mode,
            input_text,
            quality,
            allow_redownload,
            timeout=self.settings['timeout'],
            semaphore_limit=self.settings['semaphore_limit'],
            retries=self.settings['retries']
        )

    def on_cancel_clicked(self):
        """处理取消按钮点击事件"""
        self.download_manager.cancel_download()
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

    def on_log_message(self, message: str):
        """处理日志消息 - 输出到控制台"""
        print(f"日志: {message}")

    def on_download_complete(self):
        """处理下载完成事件"""
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

        # 刷新图片展示
        self.refresh_image_gallery()

        InfoBar.success(
            title='成功',
            content="下载任务已完成",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self
        )

    def on_download_error(self, error_message: str):
        """处理下载错误事件"""
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        InfoBar.error(
            title='错误',
            content=error_message,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self
        )

    def refresh_image_gallery(self):
        """刷新图片展示区"""
        # 获取下载目录
        download_dir = os.path.join(os.getcwd(), "image_downloads")
        if os.path.exists(download_dir):
            self.image_gallery.load_images(download_dir)
            # 不再调用自动滚动方法，改为鼠标触发滚动

    def toggle_theme(self):
        if self.theme == Theme.LIGHT:
            self.theme = Theme.DARK
        else:
            self.theme = Theme.LIGHT
        setTheme(self.theme)
        self.theme_button.setText("切换到" + ("浅色" if self.theme == Theme.DARK else "深色") + "主题")

    def on_navigation_changed(self, route_key: str):
        """处理导航栏切换事件"""
        if route_key == 'home':
            self.stackedWidget.setCurrentWidget(self.content_widget)
        elif route_key == 'settings':
            self.stackedWidget.setCurrentWidget(self.settings_page)